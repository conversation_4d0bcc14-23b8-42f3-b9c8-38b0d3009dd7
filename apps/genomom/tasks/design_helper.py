

import os
import re
import shutil
from datetime import datetime









def sanitize_filename(name):
    """Allow Korean, alphanumeric, underscore, dot, hyphen."""
    return re.sub(r'[^\w.\-가-힣]', '_', name)


def clean_old_results(folder_path, file_prefix):
    """Delete previous .pdf and .jpg files starting with file_prefix."""
    try:
        print(folder_path)
        if os.path.exists(folder_path):
            
            for file_name in os.listdir(folder_path):
                print(file_name)
                if file_name.startswith(file_prefix) and (
                    file_name.endswith('.pdf') or file_name.endswith('.jpg')
                ):
                    os.remove(os.path.join(folder_path, file_name))
                    print(  "Deleting file ", file_name  , folder_path)
    except Exception as e:
        print(f"Error cleaning results in {folder_path}: {e}")


def delete_image_folder_and_zip(folder_path):
    """Delete the image result folder and its zip file."""
    try:
        if os.path.exists(folder_path):
            shutil.rmtree(folder_path)
        zip_path = f"{folder_path}.zip"
        if os.path.exists(zip_path):
            os.remove(zip_path)
    except Exception as e:
        print(f"Error deleting image folder or zip: {e}")


def delete_results_folder(folder_path):
    """
    Move the results folder to /BiO/result_reset_history/ with a timestamp,
    instead of deleting it permanently.
    """
    try:
        if os.path.exists(folder_path):
            # Extract original folder name
            folder_name = os.path.basename(folder_path.rstrip('/'))

            # Generate timestamped backup folder name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = f"/BiO/result_reset_history/{folder_name}_{timestamp}"

            # Ensure backup root directory exists
            os.makedirs("/BiO/result_reset_history/", exist_ok=True)

            # Move the folder
            shutil.move(folder_path, backup_dir)

            print(f"[BACKUP] Moved folder to: {backup_dir}")
    except Exception as e:
        print(f"[ERROR] Failed to move folder {folder_path} to backup: {e}")
