
import matplotlib


# from geno_ai.fastq_download import fastq_download

import os

import shutil

# #! External Library Import
from pdf2image import convert_from_path

from datetime import datetime


from celery import shared_task

from django.utils import timezone
from django.conf import settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.forms import model_to_dict
from django.http import HttpResponse

from django.template.loader import get_template

from apps.common.log import path_check


from apps.genomom.models import (
    SampleInfo,
    Logo_Sign,
    Ppv_Npv,
    Trisomy,
    Ppv_Npv
)
from apps.genomom.weasyprint import build_pdf

from .design_helper import sanitize_filename, clean_old_results, delete_image_folder_and_zip, delete_results_folder

matplotlib.use('agg')

#root_folder = "results"
root_folder = "results_precision"











def get_header_main_footer_template(sample, service_type):
    """
    according to sample or service result the header might be different
    for korean/english normal result, failed , re-draw and dankook there are different header 

    this will return header , footer and main body template 

    we are assigning header and footer now in template directly 
    celery -A core worker -l INFO --concurrency=1

    """

    language_type = "english" if "eng" in sample.get_language_type_display().lower() else "korean"

    #! Remaining Header Footers are same only differnce in language
    if sample.samples_trisomy.final_result2 in ["0", "1", "2"]:        
        main_template = f'genomom/{root_folder}/geno_ai_result_{service_type}_{language_type}.html'

    #! incase Fail or Re-Draw
    elif sample.samples_trisomy.final_result2 in ["6", "7"]:
        #! 6 is re-dra and 7 is fail , now days ree-draw is rarely send so 7 first
        if sample.samples_trisomy.final_result2 == "7":
            main_template = f'genomom/{root_folder}/geno_ai_result_fail_{language_type}.html'
        else:
            main_template = f'genomom/{root_folder}/geno_ai_result_redraw_{language_type}.html'

    return main_template


@shared_task
def design_build_pdf(pk, logged_in_user):
    """
    after result confirmation 1 & 2 Result design in pdf format is required.
    Generate a PDF report for a given sample, using the sample's information and data from the database.

    Args:
        sample: A `SampleInfo` object representing the sample for which to generate the report.

    Returns:
        True if the PDF report was successfully generated and saved to the `sample` object's `result_pdf`
        field, or False otherwise.

        celery -A core worker -l INFO --concurrency=3

        # Dashboard using flower 
        celery -A core flower 

    """

    #! get the sample of which to get
    sample = SampleInfo.objects.get(id=pk)

    #! In case Pass Only Data Exists for server, So first check pass or Fail and do logic seprately
    sample_dic = model_to_dict(sample)
    sample_dic["tgc_s_id"] = sample_dic["tgc_s_id"].split('_')[0]  # ! in pdf result we will not keep R1_V1
    

    #! Service type can affect result template
    service_type = sample.service_type.pdf_name.lower()  # ! Get Name In English
    

    
    #! this might be lite, std , twin_lite , twin_std, twin_plus
    availiable_pdfs = ["lite", "std", "plus", "twin_lite", "twin_std"]
    
    if not service_type in availiable_pdfs:
        text = f"service type {service_type} is not selected properly contact IT team or check service pdf_name are exist or not in {availiable_pdfs} "
        path_check(text=text, root_dir=sample.sample_path())
        

    #! Show process Start
    sample.pdf_design_status = '1'  # ! Running
    sample.save()

    #! we have loaded all images in logo_sign update, Last is the latest whish will apply in result
    logo_sign = Logo_Sign.objects.last()
    main_template = get_header_main_footer_template( sample=sample, service_type=service_type)

    
    #! Trisomy High Risk Low Risk
    # ! inside template for loop will use chr1 to 24 판독
    trisomy = Trisomy.objects.get(sample=sample)
    #! get the main database on the basis of service wise
    #! lite and twin_lite are same, std and twin_std same , plus and twin_plus same
    if sample.patient.fetus_number == "v_twin":
        if service_type == "twin_lite":
            # print(
            #     f"applying {service_type} service but vanishing twin so single tone ppv Standard service aca 3 types ")
            ppv_npv = Ppv_Npv.objects.filter(**{ "lite": True})
        else:
            # print(f"applyin  {service_type} service but vanishing twin so single tone ppv Standard service aca 6 types ")
            ppv_npv = Ppv_Npv.objects.filter(**{"std": True})
        
    else:
        # ppv_npv = Ppv_Npv.objects.filter(**{service_type: True})
        
        if hasattr(Ppv_Npv, service_type):
            ppv_npv = Ppv_Npv.objects.filter(**{service_type: True})


        else:
            ppv_npv = Ppv_Npv.objects.none()  # Returns an empty queryset

                
        
        
    
    sca_ppv = ppv_npv.filter(aneup_type="sca")
    cnv_dup_ppv = ppv_npv.filter(aneup_type="cnv_dup").order_by('ordering')
    
    if service_type == 'std':
        cnv_del_ppv = ppv_npv.filter(aneup_type="cnv_del").order_by('ordering')
        aca_ppv = ppv_npv.filter(aneup_type="aca").order_by('ordering')
        
    else:
        cnv_del_ppv = ppv_npv.filter(aneup_type="cnv_del").order_by('pk')
        
        
        aca_ppv = ppv_npv.filter(aneup_type="aca").order_by('ordering')
        
   


    #! In case of Lite & Twin service readl data will apply
    aca_common_ppv = ppv_npv.filter(name__in=['13', '18', '21']).order_by("ordering")  # ! can also done by ordering value changing
    
    #! Context for standard low, high, borderline not for fail or other
    context = {
        "sample": sample,
        "logo_sign": logo_sign,
        "sca_ppv": sca_ppv,
        "aca_ppv": aca_ppv,
        'aca_common_ppv': aca_common_ppv,
        "trisomy": trisomy,
        "cnv_dup_ppv": cnv_dup_ppv,
        "cnv_del_ppv": cnv_del_ppv,
    }

    main_template = get_template(template_name=main_template)
    main_template = main_template.render(context)

    # create pdf file.
    try:
        text = f"PDF of Sample {sample.tgc_s_id} creating  at {datetime.now()} by {logged_in_user} .\n"
        path_check(text=text, root_dir=sample.sample_path())
        pdf_file = build_pdf( main_template, header_html=None, footer_html=None)

    except Exception as err:
        text = f"PDF design fail {sample.tgc_s_id} trying  at {datetime.now()} by {logged_in_user} .\n {err} "
        path_check(text=text, root_dir=sample.sample_path())
        #! if Run without any Error
        sample.pdf_design_status = '4'  # Finished
        sample.save()

    http_response = HttpResponse(pdf_file, content_type='application/pdf')
    http_response['Content-Disposition'] = 'filename="report.pdf"'

    pdf_name = sample.pdf_result_file_name() + ".pdf"

    pdf_path = os.path.join(settings.MEDIA_ROOT, sample.sample_path(), f"results") #! root_folder make results 
    
    # Try to Delete pdf_path / result path folder and create again 
    # try:
    #     print(pdf_path)
    #     #! Delete and create pdf again 
    #     delete_results_folder(pdf_path  ) #! Working 
        
    # except Exception as err:
    #     print("PDF FOlder cant delete ")
    #     print(err)
        
    
    
    os.makedirs(pdf_path, exist_ok=True)  # ! Create a Folder   

    for filename in os.listdir(pdf_path):
        file_path = os.path.join(pdf_path, filename)
        if filename.endswith('.pdf') & filename.startswith(pdf_name[:10]):
            
            #! instead removing all just remove matching
            os.remove(file_path)

    sample.result_pdf = SimpleUploadedFile(
        pdf_name,
        pdf_file,
        content_type='application/pdf'
    )

    #! Celery status change here, this is because 2-차 판독에서 출고 대기로 보내기 때문에
    sample.sample_process = '8'  # ! 8 is for 결과지 완료 But not publish Yet
    sample.pdf_design_status = '4'  # ! Finished,
    sample.run_time = timezone.now()
    sample.save()

    #! Convert pdf to image file
    #! if some times user may type special character in file name or in hospital name which django change to normal format so 
    pdf_file_path = sample.result_pdf.path

    #! folder name same except .pdf extension
    images_result_folder = pdf_file_path.strip('.pdf')

    #! delete images_result folder if exist because if result re-design then conflict raised
    #!* from the top we removed results folder and created again every time designin 
    
    
    try:
        shutil.rmtree(images_result_folder)
        os.remove(f"{images_result_folder}.zip")
    except Exception as err:
        print("Here is Error message ")
        print(err)

    #! image quality may differ on the basis of Jedan so make a condition
    #! tgc, eone, sd, kcl, scl, sml, seegene
    
    # ! Create Folder with same pdf Name &
    os.makedirs(images_result_folder, exist_ok=True)
    
    
    #paper_size = (210,297) #a4
    image_dpi = 250
    if sample.patient.jedan.jedan_name.lower() in ["sml", "seegen"]:
        
        if sample.patient.jedan.jedan_name.lower() == "sml":
            
            images = convert_from_path(pdf_file_path,  # ! pdf file path
                                       dpi=200, #image_dpi,
                                       size=(740, 1047)  ,# paper_size,
                                       # fmt="jpeg",

                                    
                                    )
            
        else:
            images = convert_from_path(pdf_file_path,  # ! pdf file path
                                       dpi=image_dpi,
                                       fmt="jpeg",
                                       #size=paper_size,

                                       jpegopt={"quality": 100,
                                                "progressive": True,
                                                "optimize": True
                                                },

                                       userpw="pass",
                                       thread_count=20,
                                       )
    else:
        
        images = convert_from_path(pdf_file_path,  # ! pdf file path
                                   dpi=image_dpi,
                                   fmt="jpeg",
                                   #size=paper_size,

                                   jpegopt={"quality": 100,
                                            "progressive": True,
                                            "optimize": True
                                            },

                                   userpw="pass",
                                   thread_count=20,
                                   )
        

    #! In this method High Risk Low Risk is causing problem T21.. not included
    front_name, last_name = sample.img_result_file_name()
    
    #!
    #front_name, last_name  = later split pdf file name and split usign AGC and use front and last name
    


    for i in range(len(images)):
        images[i].save(
            f"{images_result_folder}/{front_name}_{i+1}{last_name}.jpg", 'JPEG')
        #! before last_name _ is done from function

    #! ZIP the currently Created Folder
    shutil.make_archive(images_result_folder, 'zip', images_result_folder,)

    #! add log file who did this process and when
    text = f"PDF, img, zip of image of Sample {sample.tgc_s_id} created at {datetime.now()} {images_result_folder} which is run by {logged_in_user} .\n"

    path_check(text=text, root_dir=sample.sample_path())

    #! Create again if this is Dangkook Hospital
    if sample.patient.hospital.hospital_name.startswith('단국대'):
        main_template = f'genomom/{root_folder}/geno_ai_result_dankook_korean.html'

        main_template = get_template(template_name=main_template)
        main_template = main_template.render(context)

        # create pdf file.
        try:
            text = f"PDF of Sample {sample.tgc_s_id} creating  at {datetime.now()} by {logged_in_user} .\n"
            path_check(text=text, root_dir=sample.sample_path())
            pdf_file = build_pdf(
                main_template, header_html=None, footer_html=None)

        except Exception as err:
            text = f"PDF design fail {sample.tgc_s_id} trying  at {datetime.now()} by {logged_in_user} .\n {err} "
            path_check(text=text, root_dir=sample.sample_path())
            #! if Run without any Error
            sample.pdf_design_status = '4'  # Finished
            sample.save()

        http_response = HttpResponse(pdf_file, content_type='application/pdf')
        http_response['Content-Disposition'] = 'filename="report.pdf"'

        pdf_name = sample.pdf_result_file_name() + "_.pdf"

        
        pdf_path = os.path.join(settings.MEDIA_ROOT, sample.sample_path(), f"{root_folder}")
        os.makedirs(pdf_path, exist_ok=True)  # ! Create a Folder

        for filename in os.listdir(pdf_path):
            file_path = os.path.join(pdf_path, filename)
            if filename.endswith('_.pdf') & filename.startswith(pdf_name[:10]):
                #! instead removing all just remove matching
                os.remove(file_path)
                
                

        sample.result_pdf_option = SimpleUploadedFile(
            pdf_name,
            pdf_file,
            content_type='application/pdf'
        )
        sample.save()

    return True
