from apps.genomom.weasyprint import build_pdf
from apps.genomom.models import SampleInfo, Logo_Sign
from apps.common.log import path_check
from pdf2image import convert_from_path
from django.template.loader import render_to_string
from django.http import HttpResponse
from django.forms import model_to_dict
from django.core.files.uploadedfile import SimpleUploadedFile
from django.conf import settings
from django.utils import timezone
from celery import shared_task
import os
import re
import shutil
from datetime import datetime

from .design_helper import sanitize_filename, clean_old_results, delete_image_folder_and_zip, delete_results_folder

import matplotlib
matplotlib.use('agg')


@shared_task
def design_build_pdf_rookit(pk, logged_in_user):
    #print("Rookit PDF Design Start")
    try:
        sample = SampleInfo.objects.get(id=pk)
    except SampleInfo.DoesNotExist:
        path_check(
            text=f"Sample with ID {pk} not found.", root_dir="log_rookit_errors")
        
        #print("Rookit PDF Design End id not found ")
        return False

    sample_dic = model_to_dict(sample)
    
    #sample_dic["tgc_s_id"] = sample_dic["tgc_s_id"].split('_')[0]

    service_type = sample.service_type.pdf_name.lower()
    available_pdfs = ["nipt_basic", "nipt_max", "nipt_premium", "lite","std","plus"]
    
    if service_type not in available_pdfs:
        text = f"Invalid service type '{service_type}' for sample {sample.tgc_s_id}"
        path_check(text=text, root_dir=sample.sample_path())
        #print(text)
        return False

    sample.pdf_design_status = '1'  # Running
    sample.save()

    logo_sign = Logo_Sign.objects.last()

    context = {
        "sample": sample,
        "logo_sign": logo_sign,
    }

    # 검사불능 조건 확인 (final_result2 == "7")
    if hasattr(sample, 'samples_trisomy') and sample.samples_trisomy.final_result2 == "7":
        # 검사불능인 경우 nipt_fail.html 사용
        design_name = "nipt_fail"
    else:
        # 정상 결과인 경우 기존 로직 사용
        design_name = {
            "lite": "nipt_basic",
            "nipt_basic": "nipt_basic",
            "std": "nipt_max",
            "nipt_max": "nipt_max",
            "plus": "nipt_premium",
            "nipt_premium": "nipt_premium",
        }.get(service_type, "nipt_premium")

    html_string = render_to_string(
        f'genomom/rookit_results/{design_name}.html', context)


    try:
        path_check(
            text=f"PDF of Sample {sample.tgc_s_id} started at {datetime.now()} by {logged_in_user}",
            root_dir=sample.sample_path()
        )
        pdf_file = build_pdf(html_string)
    except Exception as err:
        path_check(
            text=f"PDF build error for sample {sample.tgc_s_id} at {datetime.now()}: {err}",
            root_dir=sample.sample_path()
        )
        sample.pdf_design_status = '4'  # Finished with error
        sample.save()
        
        return False

    # Build final PDF file name
    pdf_name = sample.pdf_result_file_name_rookit() + ".pdf"
    # pdf_name.rsplit('.', 1)[0]
    file_prefix = sample.pdf_result_file_name_rookit()

    #print(file_prefix , )
    pdf_folder = os.path.join(
        settings.MEDIA_ROOT, sample.sample_path(), "results")
    

    # ❗ Delete the full folder for a fresh start
    delete_results_folder(pdf_folder)

    # Now safely recreate
    os.makedirs(pdf_folder, exist_ok=True)



    sample.result_pdf = SimpleUploadedFile(
        pdf_name,
        pdf_file,
        content_type='application/pdf'
    )

    sample.sample_process = '8'  # 결과지 완료
    sample.pdf_design_status = '4'  # Finished
    sample.run_time = timezone.now()
    sample.save()

    pdf_file_path = sample.result_pdf.path
    images_result_folder = pdf_file_path[:-4]

    # since result folder is deleted so no need to delete image folder
    # delete_image_folder_and_zip(images_result_folder)
    os.makedirs(images_result_folder, exist_ok=True)

    # Convert PDF to images
    image_dpi = 250
    jedan_name = sample.patient.jedan.jedan_name.lower()
    if jedan_name == "sml":
        images = convert_from_path(pdf_file_path, dpi=200, size=(740, 1047))
    else:
        images = convert_from_path(
            pdf_file_path,
            dpi=image_dpi,
            fmt="jpeg",
            jpegopt={"quality": 100, "progressive": True, "optimize": True},
            userpw="pass",
            thread_count=20
        )

    for i, image in enumerate(images):
        image.save(f"{images_result_folder}/{file_prefix}_{i+1}.jpg", 'JPEG')

    shutil.make_archive(images_result_folder, 'zip', images_result_folder)

    path_check(
        text=f"PDF + images created for {sample.tgc_s_id} at {datetime.now()} by {logged_in_user}",
        root_dir=sample.sample_path()
    )

    #print("Rookit PDF Design End")
    return True


