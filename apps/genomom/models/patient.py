from datetime import date, timedelta
from django.db import models
from apps.hospital.models import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, HospitalInfo, Doctor<PERSON>n<PERSON>
from simple_history.models import HistoricalRecords
from django.core.validators import MaxValueValidator, MinValueValidator
from django.utils import timezone


#from . sample import SampleInfo
def one_day_before_today():
    return timezone.localdate() - timedelta(days=1)


# Create your models here.
class PatientInfo(models.Model):
    """
    Detail about patient.
    One Patient may have more than one samples if in case Re-Draw or Re-Test
    """
    #! keep in 0,1,2 format while deploy #! if any thing change here then edit sample also need to changed 
    #! inside edit sample there is java script if else using this value information
    FETUS_CHOICES = [
        ("Single", "단태아"),
        ("Twin", "쌍태아"),
        ("v_twin", "Vanishing twin")
    ]


    TWINS_CHOICES = [
        ("identical", "일란성"),
        ("fraternal", "이란성"),
        
        
        ("DCDA", "DCDA"),
        ("MCDA", "MCDA"),
        ("MCMA", "MCMA"),
        ("-", "-")
    ]

    IVF_NUMBER = (
        ('-', '-'),
        ('1','1'),
        ('2','2'),
        ('3',"3"),
        ('4', "4"),
    )







    ULTRASOUND_REPORT_CHOICES = [
        ("1", "유"),
        ("0", "무"),
        ('-', '-')
        
    ]

    SENG_HWAHAG_TEST_CHOICES = [
        ("1", "시행(저위험)"),
        ("2", "시행(고위험)"), 
        ("0", "미시행"),
        ('-', '-')
        
        # Yes for did the test & No for did not 
    ]
    #! if choices items changed then change inside function also 
    HISTORY_CHOICES = [
        ("No", "없음"),
        ("Family", "가족력"),
        ("Past Pregnancy", "과거 임신"),
        ('-', '-')

    ]
    
    IVF_OPTIONS = (
        ("1", "유"),
        ("0", "무"),
        ('-', '-')

    )
    

    #! company tracking ID TGCㅑㅇ
    tgc_p_id = models.CharField(unique=True, max_length=15, verbose_name="TGC P ID", help_text= "TGC Patient Name")
    
    #! last name of the patient 
    last_name = models.CharField('성', max_length=100, default="*")

    #!First name of the Patient 
    first_name = models.CharField('끝이름', max_length=100, default=" " , blank= True, null = True ) #  db_column = "first_name"

    #! All Hospitals, docs related informations
    jedan = models.ForeignKey(Jedan,on_delete=models.PROTECT, related_name= "jedan_patientInfo", )
    jedan_branch = models.ForeignKey(JedanBranch, on_delete=models.PROTECT, related_name= "jedan_branch_patientInfo", null = True,blank=True)
    hospital   = models.ForeignKey(HospitalInfo, on_delete=models.PROTECT, related_name= "hospital_patientInfo", null = True, blank=True)
    doctor = models.ForeignKey(DoctorInfo, on_delete=models.PROTECT, related_name="doctor_patientInfo", null=True, blank=True )
    
    #! We assume one patient must have same number although she may have multiple samples
    hospital_chart = models.CharField(  max_length=20, verbose_name="병원 차트번호",  help_text="Hospital Chart number ", null=True, blank=True )  # unique=True,
    
    #! We assume one patient must have same number although she may have multiple samples
    doctor_depart = models.CharField(  max_length=20, verbose_name="doctor depart",  help_text="Doctor depart ", null=True, blank=True, default = "-" )  # unique=True,
    
    #! Jedan Related
    # jedan_registered_date = models.DateField(verbose_name="재단_접수일", help_text="Entry Time", default=timezone.localdate() - timedelta(days=1) )
    jedan_registered_date = models.DateField(verbose_name="재단 접수일",help_text="Entry Time",default=one_day_before_today,)


    #! assume that registerd number of Jedan
    jedan_registered_number = models.CharField(verbose_name="재단 접수 번호", max_length=100, help_text="Jedan registeration Number" )  # Logged in User
    
    #! Age of patient
    patient_age = models.IntegerField(verbose_name="나이", null=True, help_text="Age of Patient", validators=[
                                    MaxValueValidator(80),
                                    MinValueValidator(0)])  # First Time GA
    
    #! height of the patient 
    patient_height = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="신장(cm)", null=True, help_text="신장(Height in CM)")
    
    #! Weight of the Patient 
    patient_weight = models.DecimalField(max_digits=5, decimal_places=2,verbose_name="체중(kg)", null=True, help_text="체중(weight of Patient)")
    
    #! BMI can calculate using height & weight , but some times height weight not availiable so store
    patient_bmi = models.DecimalField(max_digits=4, decimal_places=2, verbose_name="BMI", help_text="BMI of Patient", null=True)


    #! IVF treatment done or not
    ivf_treatment = models.CharField(max_length = 1 , default='0', verbose_name = "IVF 시술" , choices=IVF_OPTIONS)
    ivf_number = models.CharField(max_length=1, default='-', verbose_name="IVF 건수 ", choices=IVF_NUMBER )

    #! Fetus status weather single, Twin, or Vanishing Twin
    fetus_number  = models.CharField(verbose_name="태아수",max_length=20, choices=FETUS_CHOICES,default= "단태아")
    
    fetus_sex     = models.CharField(verbose_name="성별",max_length=1,default= "", null = True, blank = True )

    twin_memo = models.CharField(max_length=200, verbose_name="쌍태아 정보",  choices=TWINS_CHOICES, default="-")
    
    vanishing_twin_memo = models.CharField(max_length=200, verbose_name="V. Twin 소실시점",  default='-')

    #! nt_size of fetus which is done in 9 10 week of pregnancy, we assume this will done once during pregnancy,some times doc write memo with it so
    nt_size = models.CharField(max_length=100, blank=True, null=True, default='-', help_text="generally decimal size but outlier exist so var char ")
    
    ultrasound_report = models.CharField(max_length=1, verbose_name="초음파 이상", help_text= "Ultrasound Report", choices=ULTRASOUND_REPORT_CHOICES, default="0")
    
    seng_hwahag_test = models.CharField(max_length=1, verbose_name="생화학적 표지자 검사", choices=SENG_HWAHAG_TEST_CHOICES, default = '0')
    
    #seng_hwahag_risk = models.CharField(max_length=20,verbose_name="생화학적 표지자 검사 결과",default= "저위험")
    
    seng_hwahag_memo = models.CharField(max_length=250,verbose_name="생화학적 표지자 검사 내용", default= "-")
    
    aneuploidy_history = models.CharField(max_length=15, verbose_name="염색체 이상 기왕력", choices=HISTORY_CHOICES, default= "No")
    aneup_history_memo = models.CharField(max_length=250, verbose_name="염색체 이상 기왕력 메모",  null=None, default = "-")
    
    clinical_note      = models.CharField(max_length=250, verbose_name= "기타 소견",null=True, default = '-')
    
    created_at = models.DateTimeField(auto_now=True) #! Result send time is created at + 1 Day  # auto_now_add=True
    last_edited = models.DateTimeField(verbose_name="최근 수정", auto_now=True)  # ! default time
    
        
    #history = HistoricalRecords()


    def __str__(self):
        return self.tgc_p_id
    
    #! these functions are use for Downloading Excel File for Old version 
    def patient_name(self):
        
        if self.first_name:
            
            return  f"{self.last_name} * {self.first_name}" 
          
          
        else:
            if self.last_name.endswith("*"):
                return self.last_name
            else:
                return "*"
            
            
    def patient_name_rookit(self):
        return f"{self.last_name}O{self.first_name}"

    
    def ivf_yes(self):
        if self.ivf_treatment == '1' :
            return "O"
        if self.ivf_treatment == '-' :
            return "-"
        return ""
    
    def ivf_no(self):
        if self.ivf_treatment == '0' :
            return "O"
    
        if self.ivf_treatment == '-' :
            return "-"
        return ""
    
    def ultra_sound_yes(self):
        if self.ultrasound_report == "1":
            return "O"
        if self.ultrasound_report == "-":
            return "-"
        
        return ''
    
    def ultra_sound_no(self):
        if self.ultrasound_report == "0":
            return "O"
        
        if self.ultrasound_report == "-":
            return "-"
        
        return '' # if other  then check input 
    
    def seng_hwahag_test_done(self):
        if self.seng_hwahag_test == "1" or  self.seng_hwahag_test == "2":
            return "O"
        
        if self.seng_hwahag_test == "-":
            return "-"
        
        
        return ''
    
    def seng_hwahag_test_not_done(self):
        if self.seng_hwahag_test.lower() == "0":
            return "O"
        
        if self.seng_hwahag_test == "-":
            return "-"

        return ''
    
    def seng_hwahag_risk(self):
        #! if not done then unknown
        if self.seng_hwahag_test.lower() == "0":
            return ""
        
        if self.seng_hwahag_test.lower() == "1":
            return "저위험"
        
        if self.seng_hwahag_test.lower() == "2":
            return "고위험"
        
        if self.seng_hwahag_test.lower() == "-":
            return "-"
        


    def aneuploidy_history_old(self):
        if self.aneuploidy_history.lower().startswith('past'):
            return "O"
        
        if self.aneuploidy_history == "-":
            return "-"
        
        return ""
    
    def aneuploidy_doesnot_exist(self) : #! 없음
        if self.aneuploidy_history == 'No':
            return "O"
        
        if self.aneuploidy_history == "-":
            return "-"
        
        return "" 
    
    
    def aneuploidy_history_family(self):
        if self.aneuploidy_history == 'Family':
            return "O"
        
        if self.aneuploidy_history == "-":
            return "-"
        return ""
    
    def aneuploidy_history_old(self):
        if self.aneuploidy_history == "Past Pregnancy":
            return "O"
        
        if self.aneuploidy_history == "-":
            return "-"
        
        return ""

    def counts(self):
        "Total Number of samples running under this patient "
        #! This is raising Error need to solve
        samples = self.samples.all()
        return len(samples)
    
    
    
    
    def get_str_dict(self):
        """
        to keep record inside log file it will be easy 
        """
        field_dict = {}
        for field in self._meta.get_fields():
            try:
                # skip not updated fields
                field_dict[field.name] = str(getattr(self, field.name))
            except:
                # wil not raise error for non existing fields
                pass

        return str(field_dict)
    
    
    def  is_released(self) :
        """
        to control weather sample released or not under this patient 
        one patient may have multiple sample for re-draw and re-test also
        
        """
        #! get all samples 
               
        # Check if any of the samples have been released with specific conditions
        return not self.samples.filter(report_publish=True, sample_process ='9').exists()
        

    def jedan_full_number(self):
        jedan_name = self.jedan.jedan_name.lower()
        jedan_date = self.jedan_registered_date.strftime("%Y%m%d") 
        
        #! in sml jedan _ is used and in other - is used 
        if jedan_name in ["sml"]:
            jedan_date = jedan_date + '_'
        else:
            jedan_date =  jedan_date + '-'
        
        #! seejene sd, 
        jedan_date = jedan_date[4:] if jedan_name.startswith("see") else "" if jedan_name in ('sd','sql') else jedan_date
        
        return f"{jedan_date}{self.jedan_registered_number}"
    
    
    
    
    def jedan_date_str(self):
        jedan_name = self.jedan.jedan_name.lower()
        
        jedan_date = self.jedan_registered_date.strftime("%Y%m%d") 
        return jedan_date[4:] if jedan_name.startswith("see") else jedan_date
        
        
        
    def hospital_chart_pdf(self):
        
        """
        since hospital  chart is unique so entry person enter data with _ to avoid unique 
        in pdf result we also need to remove _ 
        
        """
        return self.hospital_chart.strip('_')


    class Meta:
        verbose_name = "1.환자 정보 Patient"# table header in admin sites
        verbose_name_plural = "1.환자 정보 Patients"
        
        unique_together = [["hospital", "hospital_chart", "first_name"]]
