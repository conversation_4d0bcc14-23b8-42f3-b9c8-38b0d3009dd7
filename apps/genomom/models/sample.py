import os
import re
import glob
import pandas as pd 
from natsort import natsorted
from datetime import date, timedelta
from django.db import models
from django.utils import timezone


from .patient import PatientInfo
from .analysis import Analysis_Info
from .service_name import ServiceName
from simple_history.models import HistoricalRecords
from django.utils import timezone
from .rename_path import  path_rename, path_dummay_file, path_rename_fastq

from datetime import datetime, time
import pytz


#!  passing final cnv to merge with our 149 cnv service list 
CNV_del = pd.read_excel('dummy/datas/cnvs_tgc/disease_info_del.xlsx')
CNV_duple = pd.read_excel('dummy/datas/cnvs_tgc/disease_info_duple.xlsx')
OrphaCheckData = pd.read_csv('dummy/datas/cnvs_tgc/OrphaCheckData.csv')
fixed_3mbData = pd.read_excel("dummy/datas/cnvs_tgc/3mb_CNV.xlsx")


# CNV_long_del = pd.read_excel('dummy/datas/cnvs_tgc/disease_info_long_del.xlsx')
# CNV_long_duple = pd.read_excel('dummy/datas/cnvs_tgc/disease_info_long_duple.xlsx')


def sanitize_filename(name):
    # Allow Korean (가-힣), English letters, digits, hyphen, underscore, and dot
    return re.sub(r'[^\w.\-가-힣]', '_', name)

def get_release_day(creation_day):
    """
    Expectation release day is dependent on the sample entry date.
    monday, Tuesday -> Friday 
    wed -> monday
    thursday , friday -> wednesday
    sat -> thursday
    sunday -> upcoming 3 days 
    
    """
    weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']   
    
    #! in which day the sample created , date time field of django 
    creation_weekday = weekdays[creation_day.weekday()] #! this will return in int 0,1,2,3...
    # be care full for starting day 
    if creation_weekday == 'Monday':
        # Upcoming Friday 
        release_day = creation_day + timedelta(days=4 )
        return release_day
    
    if creation_weekday == 'Tuesday':
        # Upcoming Friday 
        release_day = creation_day + timedelta(days=3 )
        return release_day
    
    if creation_weekday == "Wednesday" :
        # coming Monday after 5 days
        release_day = creation_day + timedelta(days=5)
        
        return release_day
        
    if creation_weekday == 'Thursday':
        
        release_day = creation_day + timedelta(days=6)
        return release_day
    
    if creation_weekday == 'Friday':
        release_day = creation_day + timedelta(days=5)
        return release_day
    
    # there is very low sample entry in saturday 
    if creation_weekday == "Saturday":
        release_day = creation_day + timedelta(days=5)
        return release_day
    
    #! there is very low sample entry in saturday 
    if creation_weekday == "Sunday":
        release_day = creation_day + timedelta(days=4)
        return release_day

    return release_day
    
class SampleInfo(models.Model):
    """
    Sample input information, this might be from hospital or from internally.
    """
    SAMPLE_CHOICES = [
        ("Whole Blood", "Whole Blood"),
        ("Plasma", "Plasma (혈장)")
    ]


    # given patient or sample will increase reveneu or not in organization
    TEST = (
        ('s', 'Service'),
        ('t', 'Test'),
        ('f', 'Free'),
        ('r', 'Research'),
    )
    #! pdf file result created or not logic Convert to True or False
    RESULT_DESIGN = (
        ('0', '0'),  # it means result design does not made
        ('1', '1')  # Done
    )

    #! Celery status in pdf design and pipeline running page
    CELERY_STATUS =  (
        ('0', 'New'),  # it means result design does not made
        ('1', 'queued'),
        ('2', 'Running'),
        ('3', "Error "),
        ('4', "Finished"),
        
        
    )  # Done
    
    #! dont change this english value because this are filtered as condition in entry
    TEST_TYPE = (
        ("First", "신규"), # 신규
        ("Retest", "Retest"),
        ("Redraw", "재채혈")
    )

    #! In which step the sample is exist 
    # #!* while deploy change these to verbal
    # this step will used to filter to display contents so dont change , if changed then change in other field also, datas are rendering on this basis
    STEPS = (
        ('0', '검체 입고'),  # search and add hospital chart
        ('1', '입고 확인'),  # sample info updated from entry page
        ('2', 'DNA Prep'),  # Qc analysis, Retest_p
        ('3', 'DNA Lib'),  # Qc update , Retest_L
        ('4', 'DNA Seq'),  # Qc       , Retest_S
        ('5', '분석 실행'),  # Qc sample send to confirmation
        ('6', '1차 판독'),  # Qc sample send to design result 파이프라인 실행
        ('7', '2차 판독'),  # Qc change after pdf result file
        ('8', '출고대기'),
        ('9','출고'),        
        ('10','Inactive'), # we set inactive sample to 10 
    )

    OG_OPTIONS = (
        ("1", "유"),
        ("0", "무"),
        ('-', '-')
    )
    LANGUAGE_OPTIONS = (
        ("0", "Eng"),
        ("1", "Kor")
    )
    
    GENDER_OPTIONS =  (
        ('1', "네"),
        ("0", "아니요")
    )
    
    
    
    
    #! patient , One Patient may have more than 1 samples
    patient = models.ForeignKey(PatientInfo, on_delete=models.CASCADE, related_name="samples", )
    

    
    #! Sample ID for internal Tag
    tgc_s_id = models.CharField(unique=True, max_length=20, verbose_name="Sample ID", help_text= "TGC Sample ID")
    
    #! Test type is weather first_type, Re-Draw , Re-Seq , .... 
    test_type    = models.CharField(max_length=15, choices=TEST_TYPE, verbose_name= "분석 종류", default= "First" )
    
    #! Patient is doing sample for free or service or research purpose
    service_category = models.CharField(verbose_name= 'Service Category', max_length=1, choices=TEST, default="s")
    
    #! entry date, received date & created_at same thing
    test_date = models.DateField( verbose_name="실험일", default=timezone.now,editable=True,null= True, blank= True )
        
    #! Service Type lite, plus or Standard, ..., importing from another table give more flexibility
    service_type = models.ForeignKey( to = ServiceName, null=True, verbose_name="검사 항목", on_delete=models.PROTECT, default=2 )

    #! Blood Extraction on which date, this is in Entry, each samples has different
    extraction_date = models.DateField(verbose_name="채혈일시" ,null=True, help_text= "Extraction date")

    #! entry date, received date & created_at same thing
    entry_date = models.DateField( verbose_name="입고일", default=timezone.localdate ,editable=True,)
    
    #! in case re-test multiple times only one sample result will send 
    result_sent_time = models.DateTimeField(verbose_name="출고 예상 일", null=True, blank=True) 
    
    #! For Sample Redraw one patient may have multiple ga so 
    patient_ga = models.DecimalField(max_digits=3, decimal_places=1, null=True,blank=True, verbose_name="임신주수(e.g. 10.6)",
                                     help_text= "Pregnancy week_day")
    
    #! sample memo, From Entry Point to Exit Point
    sample_memo = models.CharField(max_length=255, verbose_name= "특이사항", null=True,default = '-', blank=True)
    
    #! sample memo, From Entry Point to Exit Point
    jedan_memo = models.CharField(max_length=255, verbose_name= "전달용 메모", null=True,default = '-', blank=True)

    #! Samples Types either blood or Plasma, convert choice 
    sample_type = models.CharField(verbose_name="검체 종류", max_length=20, choices=SAMPLE_CHOICES, default= "Plasma")
    
    #! Weather Original Tube was Used or not While collecting Blood / Sample 
    tube_og     = models.CharField(verbose_name="전용튜브 유무", default='1', choices=OG_OPTIONS, max_length=5)
    
    #! Common Path for Sample related files to store
    path_root =  models.CharField(max_length=100,null=True, default='./gm_ai/test_samples') # Files & Folders

    #! Step wise sample process, each page different step
    sample_process = models.CharField(max_length = 2, verbose_name= "분석 단계", choices=STEPS, default='0')
    
    #! analysis Server Information packages, In case analysis info delete keep null value , DNA QC2 update
    analysis = models.ForeignKey(Analysis_Info, on_delete=models.SET_NULL , related_name="analysis_sampleInfo", null=True, blank=True)
    
    #! When to Run PipeLine in the server and celery status for easily view , also use for pdf creation time 
    run_time = models.DateTimeField(null=True, blank=True)
    
    #! in pipeline run page progress bar will display on this value basis 
    pipeline_progress = models.IntegerField(verbose_name="Pipeline Progress", default=0, help_text="show the progress of NIPT pipeline progress")

    #! to monitor weather celery is running, completed or any Error raised
    pipeline_status = models.CharField(max_length = 1, verbose_name="Pipeline Run Status", default='0',choices=CELERY_STATUS ,  help_text="Show Pipeline status")

    #! Celery Task will save here , when we cancel or reset then this will use to revoke the close running celery
    celery_task = models.CharField(
        max_length=200, verbose_name="Celery Task ID ", null=True, default='-', blank=True)
    
    #! pdf result language type  0 is english 1 is korean default is 1 Korean
    gender = models.CharField(verbose_name="성별 표시", max_length=1,
                                     choices=GENDER_OPTIONS, default='0', help_text="Gender info in pdf")


    #! pdf result language type  0 is english 1 is korean default is 1 Korean
    language_type = models.CharField(verbose_name="결과지 언어",max_length=1,choices=LANGUAGE_OPTIONS, default='1', help_text="PDF language")

    #! Same  as Pipeline progress to check weather pdf file created successfully or not 
    pdf_design_status = models.CharField(verbose_name="PDF creation status", default='0', max_length=1,choices=CELERY_STATUS,help_text="Celery Status in pdf design status")


    #! Result to send for Hospital, Internal pdf will download directly using path 
    result_pdf = models.FileField(max_length=255, upload_to= path_rename,  blank=True  ) 
    
    #! Result to send for Hospital, Internal pdf will download directly using path 
    result_pdf_option = models.FileField(max_length=255, upload_to= path_rename,  blank=True  ) 
    
    #! keep record how many times pdf downloaded
    pdf_downlod = models.IntegerField(verbose_name="Pdf downloded ", default=0, help_text="How many times pdf downloaded")
    
    #! Fastq 파일 업로드 필드
    fastq_upload = models.FileField(max_length=255, upload_to=path_rename_fastq, blank=True, verbose_name="Fastq 파일")
    
    #! after sample release then this will be True and apply to download pdf or image file 
    result_status = models.BooleanField(verbose_name = "Release Status",  default=False) 
    
    #! ONly True samples will display in Jedan page like re-test will not display because will false
    report_publish = models.BooleanField(verbose_name= "출고-Release?", default=True  )

    is_active = models.BooleanField(verbose_name= "실행중 Active ?" , default=True) #! we only performs active sample, Redraw, Re-seq, ... will change to inactive
    
    is_manual = models.BooleanField(verbose_name= "Fastq 수동 실행" , default=False , help_text = "copy Fastq to its location after reset ") #! we only performs active sample, Redraw, Re-seq, ... will change to inactive
    
    
    # ! Sample Entry Re-Check before release
    is_re_checked = models.BooleanField(verbose_name= "검수 확인 ?", default=False)


    #! Not Using any more
    received_by = models.CharField(max_length=30,verbose_name="입고 담당자",  default="-") # Logged in User Name Auto input
    
    created_at = models.DateTimeField(auto_now_add=True) #! Result send time is created at + 1 Day  # auto_now_add=True
    
    
    last_edited = models.DateField(verbose_name="최근 수정", auto_now=True)  # ! default time
    
    #! Track History of Sample Table 
    #history = HistoricalRecords()
    

    def __str__(self):
        return str(self.tgc_s_id)
    


    def sample_path(self) :
        """
        in this way always create a correct path , and cant assign same path 
        """
        return os.path.join(self.path_root , self.tgc_s_id )


    
    def biggish_file(self):
        """
        This function is used on to download the zip folder.
        return the path of pdf result of patient.
        genobro result page
        #! when time availiable remove this and apply if condition 
        """
        #load the file
        if self.result_pdf:
            return self.result_pdf.path
        

    def get_hospital_name(self):
        "get the hospital Name from patient table> hospital_table, use for adminn page"
        try:
            return self.patient.hospital.hospital_name
        except :
            return "-not registered"

    def fetus_info(self):
        "get weather single , twin or Vanishing Twin from"
        return self.patient.fetus_number
    
    def patient_name(self):
        return self.patient.first_name
    
    def seq1(self):
        try:
            return self.ff_info.seq1
        except :
            return "-"
        
    def geno_ai(self):
        try:
            return self.ff_info.geno_ai
        except:
            return "-"
        
    def ff_y(self):
        try:
            return round( self.ff_info.ff_y ,2)
        except:
            return "-"
        
    def get_days(self):
        """
        this will display TAT days by excluding saturday , Sunday and korean Holidays
        """
        
        try:
            delta = date.today() - self.entry_date
            
            total_days = delta.days

            # List of Korean holidays (replace with actual holiday dates)
            korean_holidays = [ 
                                date(2023, 9, 28), date(2023, 9, 29), date(2023, 10,3 ), date(2023, 10,9 ), date(2023, 12,25 ),    ]  # Add more holidays

            # Calculate the number of weekends and Korean holidays within the time period
            excluded_days = 0
            for i in range(total_days + 1):
                day = self.entry_date + timedelta(days=i)
                if day.weekday() in [5, 6]:  # 5 is Saturday, 6 is Sunday
                    excluded_days += 1
                elif day in korean_holidays:
                    excluded_days += 1

            # Subtract excluded days from the total days and add 1 for including the starting day
            adjusted_days = total_days - excluded_days + 1

            return adjusted_days
        except:
            return "*"
        
       
            
    def patient_ga_week(self):
        """
        while Downloading GA week and day seprately user must input either in format 10_05 or 10.05 format
        if other format then first 2 characters will be use for 
        we receive ga in Float Field 
        """
        try:
            return str(self.patient_ga).split('.')[0]  # 12.5 => 12
        except:
            return "-"

    def patient_ga_day(self):
        """
        while Downloading GA week and day seprately user must input either in format 10_05 or 10.05 format
        if other format then first 2 characters will be use for 
        """
        try:
            return str(self.patient_ga).split('.')[-1]  # 12.5 => 5
        except:
            return "-"

    def patient_aw(self):
        try:
            return f"{self.patient.patient_age}_{self.patient_ga_week()}"
        except :
            return "32_12"
            
            
    def sample_ga_week_day(self):
        """
        while Downloading GA week and day seprately user must input either in format 10_05 or 10.05 format
        if other format then first 2 characters will be use for 
        """
        
        try:
            # 12.5 => 12
            return str(self.patient_ga).split('.')[0] + " 주 " + str(self.patient_ga).split('.')[-1] + " 일"
        except:
            return "-"
    
    def save(self, *args, **kwargs):
        """
        expected release date on the basis of sample Entry date
        """
        #print(self.result_sent_time )
        if not self.result_sent_time:
            korean_time_zone = pytz.timezone('Asia/Seoul')
            
            #! this will stop to overide 
            release_date = get_release_day(self.entry_date)
            
            #! Release time is 3 pm Korean time default
            # Create a time object for 3:00 PM
            new_time = time(15, 20)  # 3:00 PM
            # Combine the date from datetime_obj with the new_time and timezone
            release_date = korean_time_zone.localize(datetime.combine(release_date , new_time))
            self.result_sent_time = release_date
            
        
        #! Multiple Value 
        super().save(*args, **kwargs)
    
    def pdf_result_file_name(self):
        """
        file name for pdf result 
        Jedan code is different for different service.
        Hospital jedan code is stored blank if not anything given.
        Hospital Chart is in last so add later in another parts
        
        """
                
        service_type = self.service_type.pdf_name.lower().strip()
        #jedan_code = "" # default 
        
        if  service_type == 'lite'  :
            jedan_code = self.patient.hospital.code_lite
            
        elif service_type == "std" :
            jedan_code = self.patient.hospital.code_std
            
        elif service_type  == "plus":
            jedan_code = self.patient.hospital.code_plus
            
            
        elif service_type == "twin_lite":
            jedan_code = self.patient.hospital.code_twin

        elif service_type == "twin_std":
            # print("service in sample model  ", service_type, self.patient.hospital.code_twin_std)
            jedan_code = self.patient.hospital.code_twin_std
            
            
            
        else :
            jedan_code = "-"
            #print("not matched properly contact IT ")
        # print( service_type )
            
            
            
        # elif 'tw' in self.patient.fetus_number.lower() :# or self.patient.fetus_number.lower() == "트윈"  :
        #     #! twin , vanishing twin 
        #     service_type = 'code_twin'
        #     #! IN SCL Vanishing Twin code is same as 
        #     jedan_code = self.patient.hospital.code_twin


        

        #! For Fail or Re-draw the pattern is different 
        if self.patient.jedan.jedan_name.lower() in  ["sml" ]:
            if self.samples_trisomy.final_result2 in ["0",'1','2'] :
                #! 0:Low Risk, 1 High Risk, 2 Border Line
                #! Only result for Low, Border line and High Risk
                return f"{self.patient.jedan_full_number()}_{jedan_code}_{self.patient.tgc_p_id}_{self.patient.hospital_chart_pdf()}{self.samples_trisomy.aneuploidy()}"
            
            # #! if want to apply fail seprately then 
            # elif self.samples_trisomy.final_result2 =='7' :
            #     return f"{self.patient.jedan_full_number()}_{jedan_code}_{self.patient.tgc_p_id}_{self.patient.hospital_chart_pdf()}_Fail"
                
            
            else:
                #! For Fail or Border line the pattern is different 
                return f"{self.patient.tgc_p_id}_{self.patient.jedan_full_number()}_{self.patient.hospital_chart_pdf()}_{self.samples_trisomy.aneuploidy()}"
                
                
        elif self.patient.jedan.jedan_name.lower() in  ["scl" ]:
            if self.samples_trisomy.final_result2 in ["0",'1','2'] :
                # 재단_접수_번호_재단/병원_서비스_코드_TGC_P_ID_병원_차트_번호
                return  f"{self.patient.jedan_full_number()}_{jedan_code}_{self.patient.tgc_p_id}{self.samples_trisomy.aneuploidy()}"
            
            else:
                return  f"{self.patient.jedan_full_number()}_{jedan_code}_{self.patient.tgc_p_id}_{self.samples_trisomy.aneuploidy()}"
               
        else :
            if self.samples_trisomy.final_result2 in ["0",'1','2'] :
                return f"{self.patient.tgc_p_id}_{self.patient.jedan_full_number()}_{self.patient.hospital_chart_pdf()}{self.samples_trisomy.aneuploidy()}"
            else:
                return f"{self.patient.tgc_p_id}_{self.patient.jedan_full_number()}_{self.patient.hospital_chart_pdf()}_{self.samples_trisomy.aneuploidy()}"
    
    def pdf_result_file_name_rookit(self):
        """
        Generate a safe file name for the PDF result.
        Includes service-type code, sanitized patient and hospital names, and result date.
        """

        service_type = self.service_type.pdf_name.lower().strip()

        if service_type == 'nipt_basic':
            jedan_code = "NB"
        elif service_type == "nipt_premium":
            jedan_code = "NP"
        elif service_type == "nipt_max":
            jedan_code = "NM"
        else:
            jedan_code = "RK"

        patient_name = sanitize_filename(self.patient.patient_name_rookit())
        hospital_name = sanitize_filename(self.patient.hospital.hospital_name)
        date_str = self.result_sent_time.strftime('%Y%m%d')

        if self.samples_trisomy.final_result2 in ["0", '1', '2']:
            return f"{patient_name}({jedan_code}_{hospital_name})_{date_str}{self.samples_trisomy.aneuploidy()}"
        else:
            return f"{patient_name}({jedan_code}_{hospital_name})_{date_str}_{self.samples_trisomy.aneuploidy()}"
        
        
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    def img_result_file_name(self):
        """
        file name for img result 
        Jedan code is different for different service.
        Hospital jedan code is stored blank if not anything given.
        Hospital Chart is in last so add later in another parts
        for back put '_' in this function because adding _ in 
        
        """
        
        service_type = self.service_type.pdf_name.lower().strip()

        if service_type == 'lite':
            jedan_code = self.patient.hospital.code_lite

        elif service_type == "std":
            jedan_code = self.patient.hospital.code_std

        elif service_type == "plus":
            jedan_code = self.patient.hospital.code_plus

        elif service_type == "twin_lite":
            jedan_code = self.patient.hospital.code_twin
            
        elif service_type == "twin_std":
            # print("service in sample model  ", service_type , self.patient.hospital.code_twin_std  )
            jedan_code = self.patient.hospital.code_twin_std

        else:
            jedan_code = "-"
            #print("not matched properly contact IT ")
        # print(service_type)
        
        
        #! For Fail or Re-draw the pattern is different 
        if self.patient.jedan.jedan_name.lower() in  ["sml",]:
            
            try:
                if self.samples_trisomy.final_result2 in ["0",'1','2'] :
                    #! 0:Low Risk, 1 High Risk, 2 Border Line
                    #! Only result for Low, Border line and High Risk
                    front_name = f"{self.patient.jedan_full_number()}_{jedan_code}"
                    back_name = f"_{self.patient.tgc_p_id}_{ self.patient.hospital_chart_pdf()}{self.samples_trisomy.aneuploidy()}"
                    
                    return front_name, back_name
                
                else:
                    #! For Fail or Border line the pattern is different 
                    front_name = f"{self.patient.tgc_p_id}_{self.patient.jedan_full_number()}{self.samples_trisomy.aneuploidy()}"
                    
                    return front_name, "" #! Back is Just Blank redraw and fail is single page format so dont matter
                    
                    
                    
            except :
                #! if 2-차 판독 not exist 
                front_name =  f"{self.patient.tgc_p_id}_{self.patient.jedan_full_number()}"
                back_name  = '' #! This is Error so should not execute 
                
                
        elif  self.patient.jedan.jedan_name.lower() in  [ 'scl']: 
            
            try:
                if self.samples_trisomy.final_result2 in ["0",'1','2'] :
                    front_name = f"{self.patient.jedan_full_number()}_{jedan_code}"
                    back_name = f"_{self.patient.tgc_p_id}{self.samples_trisomy.aneuploidy()}"
                    
                    return front_name, back_name
                
                else:
                    #! For Fail or Re-Draw line the pattern is different 
                    front_name =  f"{self.patient.tgc_p_id}_{self.patient.jedan_full_number()}"
                    back_name =  ''
                    return front_name, back_name 
                    
                    
            except :
                #! if 2-차 판독 not exist 
                front_name =  f"{self.patient.tgc_p_id}_{self.patient.jedan_full_number()}"
                back_name  = ''
                return front_name, back_name
                
                      
            
        
            
        else :
            #! if Fail or Re-Draw , or other jedan not sml & scl 
            front_name = f"{self.patient.tgc_p_id}_{self.patient.jedan_full_number()}"
            back_name  = ''
            return front_name, back_name 
            
        #! back name is common if sml then only add gc_p_id otherwise add hospital chart and aneuploidy 
    
    
    
    def get_str_dict(self):
        """
        to keep record inside log file it will be easy 
        """
        field_dict = {}
        for field in self._meta.get_fields():
            try:
                # skip not updated fields
                field_dict[field.name] = str(getattr(self, field.name))
            except:
                # wil not raise error for non existing fields
                pass
            
        return str(field_dict)
    
    



    def cnv_final_size(self):
        """
        in result confirm page this will show filter cnv final file size 
        """
        try :
            cnv_final_path = f"media/{self.sample_path()}/*final.cnv" # we skipp barcode 
            cnv_file = glob.glob(cnv_final_path)

            if cnv_file :
                df = pd.read_csv(cnv_file[0])
                cnv_final_size =  df.shape[0]
                return cnv_final_size
            else:
                return 0 #! if cnv final file does not exist then cant process ahead 
            
            #! when cnv file exist then this block will also execute
            
        except :
            return 0
    
    
    def cnv_filtered_size(self):
        """
        This will filtered final.cnv 
        
        """
        ### "Code for Checking OrphaNet References in 3 Mb CNV - 1 ###
        def generate_queries(query):
            positions = re.match(r'([0-9]{1,2})([pq][0-9\.]+)?([pq][0-9\.]+)', query)
            positions = positions.groups()
            pos_dic = {"chr":positions[0],
                    "pos_1": positions[1],
                    "pos_2": positions[2]}
            
            if pos_dic['chr'] == '23':
                pos_dic['chr'] = 'X'
            
            if pos_dic["pos_2"][0] == 'p':
                p_arm = True
            else:
                p_arm = False
                
            queries = [query]
            
            if p_arm:
                queries.append(f'{pos_dic["chr"]}{pos_dic["pos_2"]}')
                if pos_dic["pos_1"] != None:
                    queries.append(f'{pos_dic["chr"]}{pos_dic["pos_1"]}')
            else:
                if pos_dic["pos_1"] != None:
                    queries.append(f'{pos_dic["chr"]}{pos_dic["pos_1"]}')
                queries.append(f'{pos_dic["chr"]}{pos_dic["pos_2"]}')

            return queries
        
        def OrphaCheckProcess(OrphaCheckData, fixed_3mbData, df):
            df['OrphaNet'] = ''
            final_df = df.copy()
            for idx, row in df.iterrows():
                if 'disease' in row.index and row['disease'].strip() != '-':
                    final_df.at[idx, 'OrphaNet'] = 'Skip'
                else:
                    query = row['cytoBand']
                    if float(row['CopyRatio']) > 1:
                        typ = 'duplic'
                    else:
                        typ = 'deleti'
                    # 단계별 검색 수행
                    queries = generate_queries(query)
                    find_df = pd.DataFrame()
                    for q in queries:
                        # print(f'[ Query Text ]: {q} ({typ})')
                        firstCheck_data = fixed_3mbData[(fixed_3mbData['cytoband'] == q) & (fixed_3mbData['type'] == typ)]
                        if not firstCheck_data.empty:
                            find_df = pd.concat([find_df,firstCheck_data])
                        # 정규식을 사용하여 보다 엄격한 필터링 수행
                        regex_pattern = rf'(?:^|\s|[^0-9pq.]){re.escape(q)}(?:$|\s|[^0-9pq.])'
                        filtered_data = OrphaCheckData[OrphaCheckData['Name'].str.contains(regex_pattern, regex=True, case=True, na=False)]
                        filtered_data = filtered_data[filtered_data['Name'].str.contains(typ, case=True, na=False)]
                        find_df = pd.concat([find_df,filtered_data])
                    if not find_df.empty:
                        find_df = find_df.drop_duplicates()
                        
                        # find_df의 모든 'Name/OrphaCode' 조합을 ', '로 연결하여 저장
                        orpha_net_values = find_df[['Name', 'OrphaCode']].apply(lambda x: f"{x['Name']}/{x['OrphaCode']}", axis=1)
                        final_df.at[idx, 'OrphaNet'] = ', '.join(orpha_net_values)
                    else:
                        final_df.at[idx, 'OrphaNet'] = 'Unknown'
                        
            cols = final_df.columns.tolist()
            cytoBand_index = cols.index("cytoBand")
            cols.insert(cytoBand_index + 1, cols.pop(cols.index("OrphaNet")))
            final_df = final_df[cols]  # Reorder the DataFrame

            return final_df

        
        try:
            # cnv_filtered_path = os.path.join(os.path.join("media",self.sample_path(), '/*final_filtered.cnv'))
            cnv_filtered_full_path = os.path.join(os.path.join("media",self.sample_path(), f'{self.tgc_s_id}_final_filtered.cnv') )


            cnv_filtered_path = glob.glob(cnv_filtered_full_path  )
            
            # print(cnv_filtered_path, cnv_filtered_path)
            
            if cnv_filtered_path  :
                # print("Filtered CNV file exist ")
                df = pd.read_csv(cnv_filtered_path[0])
                
                if df.shape[0]:
                    return   df.shape[0]
                else:
                    return 0
            
            else:
                #print("Filtered CNV doesnot exist ", self.tgc_s_id )
                #! Create cnv filtered 
                cnv_final_path = os.path.join(os.path.join("media",self.sample_path(), '*final.cnv')) # f"media/{self.sample_path}/*final.cnv" # we skipp barcode 
                

                cnv_final_path = glob.glob(cnv_final_path)
                
                                
                if cnv_final_path :
                    
                    CNV_result = pd.read_table(cnv_final_path[0], sep = "\t")
                    if not CNV_result.shape[0] :
                        #! for next step fast processing write blank data Frame 
                        _ = pd.DataFrame(columns=["blank_data_base"])
                        _.to_csv( cnv_filtered_full_path )
                        #print("Saved Blank Data Frame ", self.tgc_s_id )

                        return 0
                           
                    #! Length is original Human Readable size 
                    CNV_result["Length_"]   = CNV_result["Length"].copy()
                    
                    #! Create size by reducing    
                    CNV_result["Length"] = CNV_result.End - CNV_result.Start
                    
                    #! Remove those which are less than 1M
                    CNV_result = CNV_result[CNV_result["Length"] > 10_00_000].reset_index(drop = True)
                    
                    if not CNV_result.shape[0] :
                        #! for next step fast processing write blank data Frame 
                        _ = pd.DataFrame(columns=["blank_data_base"])
                        _.to_csv( cnv_filtered_full_path )
                        #print("Saved Blank Data Frame ", self.tgc_s_id )
                        return 0
                    
                    final_result = pd.DataFrame() 
                    ## we need to loop this CNV_result in our screening list excel
                    ## CNㅍ_del, CNV_dup, CNV_long_del , CNV_long_duple 
                    for index in CNV_result.index :
                        # run each line and check weather filtered is exist in our range or not 
                        sample = CNV_result.loc[[index]]
                        #! Extract common values chr number , start & end position of single row 
                        sample_chr = sample.loc[index, "Chr" ]
                        sample_start = sample.loc[index , "Start" ] 
                        sample_end   = sample.loc[ index , "End" ]
                        sample_copy_ratio = sample.loc[index, "CopyRatio"] # greater than 1 is duplication , and less is deletion , ignore x, y now 
                        
                        #! lets check each row weather exist in our deletion , duplication , range 
                        if sample_copy_ratio < 1:
                            select_df = CNV_del[CNV_del['chr'] == sample_chr ]                    
                        else: # for Duplication greater than 1 , using duplication table short and long duplication
                            
                            select_df = CNV_duple[CNV_duple['chr'] == sample_chr ] 
                    
                        # now loop in selected df and append in sample 
                        for i, row in select_df.iterrows():
                            if row.start <= sample_end and row.end >= sample_start :
                                sample["disease"] = row.disease  #! Sample is a single Row 
                                
                                #! Added 2024-08-26 to show actual filter size 
                                #!* from here 
                                #! get intersection size
                                if row.start > sample_start:
                                    start_posistion = row.start
                                else:
                                    start_posistion = sample_start

                                if row.end < sample_end:
                                    end_position = row.end
                                else:
                                    end_position = sample_end

                                filterd_size = end_position - start_posistion
                                
                                sample["Intersection_Size"] = round(
                                    filterd_size / 10_00_000, 2)  # ! Sample is a single Row

                                sample["Service_start"] = row.start
                                sample["Service_end"] = row.end
                                sample["Service_size"] = row.end - row.start
                                sample["Service_size"] = round(
                                    sample["Service_size"] / 10_00_000, 2)
                                
                                #!* ************** up to here 
                                
                                #! Rename m to Mb
                                #! Converting to Mb 
                                sample["Intersection_Size"] = sample["Intersection_Size"].astype(str) + "Mb"
                                sample["Service_size"]  = sample["Service_size"].astype(str).astype(str) + "Mb" 
                                
                                
                                sample["RiskGene"]  = row["coreGene"]
                                
                                exist_gene = ''
                                exist_count = 0
                                
                                if row["coreGene"].lower() != "unknow" :
                                    all_risks_genes = row["coreGene"].split(';')
                                                                        
                                    for single_risk_gene in all_risks_genes:
                                        if not single_risk_gene :
                                            #! in last there might be blank 
                                            continue
                                        
                                        if single_risk_gene in  sample.loc[index, "refGene" ]   :
                                            exist_gene += f"{single_risk_gene};"
                                            exist_count += 1
                                exist_gene = exist_gene.strip(';')
                                total_gene = len(sample.loc[index, "refGene" ].split(';'))
                                sample["ExistGene"]  =f"{exist_gene} /  [ {exist_count}/ {total_gene} ]"
                                final_result = pd.concat([ final_result, sample ] , ignore_index= True )
                    
                              
                    
                    #full_path = os.path.join(os.path.join("media",self.sample_path(), f'{self.tgc_s_id}_final_filtered.cnv') )
                    CNV_result = CNV_result[CNV_result.Length > 3_000_000 ]
                    if final_result.shape[0] :
                        # print("####" * 10 )
                        # nan is creating long fraction point 
                        final_result["Service_start"] = final_result["Service_start"].astype(str)
                        final_result["Service_end"] = final_result["Service_end"].astype(str)
                        
                        final_result = pd.merge(final_result, CNV_result , on = CNV_result.columns.to_list() , how="outer",   ).fillna( '-' ) 
                        
                        #! This is not working because in rendering processing auto fill is done 
                        # final_result["CopyRatio"] = final_result["CopyRatio"].astype(str).str[:2]
                        # final_result["Population-Test"] = final_result["Population-Test"].astype(str).str[:2]
                        
                        # final_result["CopyRatio"] = final_result["CopyRatio"].astype(float).round(3) #.apply(lambda x: '{:.3f}'.format(x).rstrip('0').rstrip('.'))
                        # final_result["Population-Test"] = final_result["Population-Test"].astype(float).round(3)#.apply(lambda x: '{:.3f}'.format(x).rstrip('0').rstrip('.'))
                        
                        # print(final_result )
                        
                        final_result["Length"]  = final_result["Length_"].copy()
                        final_result.drop(columns=["Length_", ], inplace = True )
                        
                        #! even blank file we will create a file so next step will render 0 
                        try:
                            final_result =  final_result.iloc[:, [-7,-6, -5, -4, -3, -2, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]]
                        except Exception as err :
                            print(err)
                            pass
                    else:
                        if CNV_result.shape[0] >=1 :
                            final_result = CNV_result
                        
                        # print("dropped ")
                    #! Check weather RiskGene How many Exist in refGene
                    
                    
                    # in final result M replace with Mb
                    final_result["Length"] = final_result["Length"].astype(str).str.replace("M","Mb").str.replace('K', 'Kb')
                    
                    ### "Code for Checking OrphaNet References in 3 Mb CNV - 2 ###
                    final_result = OrphaCheckProcess(OrphaCheckData, fixed_3mbData, final_result)
                    
                    
                    final_result.to_csv( cnv_filtered_full_path , index=False)
                    
                    #print("Final CNV Saved in Path ", cnv_filtered_full_path )
                    return final_result.shape[0]
    
                else :
                    #print("CNV File does not exist "  )
                    #! Just write down blank CNV filtered file 
                    _ = pd.DataFrame(columns=["blank_data_base"])
                    _.to_csv( cnv_filtered_full_path )
                    #print("Saved File ")
                    return 0
                
        except Exception as err :
            print(err , self.tgc_s_id )
            print("***"* 100 )
            
            
            return 0
               
               
               
    def cnv_max_size(self):
        """
        if filtered cnv exist then return max size so we can easily filter in confirm page 
        23 has big size so skipping this 
        
        
        """
        try:
            # cnv_filtered_path = os.path.join(os.path.join("media",self.sample_path(), '/*final_filtered.cnv'))
            cnv_filtered_full_path = os.path.join(os.path.join("media",self.sample_path(), f'{self.tgc_s_id}_final_filtered.cnv') )

            cnv_filtered_path = glob.glob(cnv_filtered_full_path  )
            
            if cnv_filtered_path  :
                # print("Filtered CNV file exist ")
                df = pd.read_csv(cnv_filtered_path[0])
                df = df.loc[df.Chr != 23 ] #! 23 has very long del duplication
                              
                if not df.empty:
                    
                    
                    df["length"] = round( (df.End - df.Start)/10_00_000 , 1 )
                    
                    return df.length.max()
                
                else:
                    return 0
               
            return '*'
        except Exception as err:
            
            return '-'

               
               
    def ts_result(self):
        """
        ts_result is information file written while downloading fastq 
        if this file doesnot exist then return False 
        so in template we can use condition
        """
        try:
            # f"media/{self.sample_path}/*final_filtered.cnv"
            ts_result_path = os.path.join("media", self.sample_path() , "TS_result" )
            
            # cnv_final_files = glob.glob( f"media/{self.sample_path}/*_final_filtered.cnv" )
            ts_result_file = glob.glob( ts_result_path  )
            
            if ts_result_file:
                
                ts_result_file = ts_result_file[0]
                df = pd.read_table(ts_result_file)
                
                df.rename(columns={"Mean_Read_Length":"MRL"}, inplace = True )
                #! Ignore last column Reference Gene
                #df = df.iloc[:, :-1]
    

                # Render the styled DataFrame as an HTML table
                html_table = df.to_html(index=False,).replace('nan', '')

                html_table = html_table.replace(
                    '<table', '<table class="table table-bordered table-hover"')

                html_table = html_table.replace(
                    '<th', '<th style="padding:5px;"')

                # Find and remove the index column from the table headers
                index_column_header = '<th class="blank"></th>'
                html_table = html_table.replace(index_column_header, '')

                return html_table
            else :
                return "<h1> TS result file does not exist  </h1>"
            
        except FileNotFoundError:
            return "<h1> TS result file  not found</h1>"

        except pd.errors.EmptyDataError:
            return "<h1>TS result  file is empty</h1>"
        
        
             
            
    def chip_info_images(self):
        chip_image_dir = os.path.join('media/' , self.sample_path() , "chip_info_images")
        
        os.makedirs(chip_image_dir , exist_ok=True)
        chip_images = [chip_image_dir + '/' + chip_image for chip_image in os.listdir(chip_image_dir ) if chip_image.endswith('.png')]
        
        #! add single Normal Distribution image 
        chip_images .append(
            f"media/{self.sample_path()}/TS_histogram.png")
        
        return chip_images   
    
    
    
        
    def cnv_images(self):
        """
        currently this is using , but can remove now, this is only using is sample detail page to render all cnv images
        """
        cnv_image_dir = os.path.join( 'media', self.sample_path() ,  "cnv_images")
        # print(cnv_image_dir)

        # cnv_image_dir = cnv_image_dir.replace('/', '/')
        os.makedirs(cnv_image_dir , exist_ok=True)
        cnv_images = [cnv_image_dir + "/" + cnv_image for cnv_image in natsorted(
            os.listdir(cnv_image_dir)) if cnv_image.endswith(".png")]
        
        return  cnv_images
    
    
    
            
        
        
    def sca_images(self):
        sca_image_dir = os.path.join('media/' , self.sample_path() , "sca_images")
        os.makedirs(sca_image_dir , exist_ok=True)
        sca_images = [sca_image_dir + '/' + sca_image for sca_image in natsorted(os.listdir(sca_image_dir)) if sca_image.endswith('.png')]
        return sca_images
    

    def z_scores_images(self):
        
        z_scores_image_dir = os.path.join('media/' , self.sample_path() , "z_scores_images" )
        os.makedirs(z_scores_image_dir , exist_ok=True)
        return [z_scores_image_dir  + '/' + z_image for z_image in natsorted(os.listdir(z_scores_image_dir)) if z_image.endswith('.png')]
        
    def genoai_images(self):

        genoai_image_dir = os.path.join(
            'media/', self.sample_path(), "rc_images_30")
        
        os.makedirs(genoai_image_dir, exist_ok=True)
        return [genoai_image_dir + '/' + ai_image for ai_image in natsorted(os.listdir(genoai_image_dir)) if ai_image.endswith('.jpg')]
        
        
        
        
    def ai_prediction_image(self):
        
        all_files = os.listdir( os.path.join('media', self.sample_path()  ))
        
        for file in all_files:
            if file.endswith("_AIM.png"):
                return os.path.join('media', self.sample_path() , file)
        return '-'
    
    def jedan_number(self):
        if self.patient.jedan.jedan_name.lower().startswith("sd"):
            return  self.patient.jedan_registered_number
        
        # elif self.patient.jedan.jedan_name.lower().startswith("scl"):
        #     return date.strftime(self.patient.jedan_registered_date, "%m%d") + '_' + self.patient.jedan_registered_number
            
        elif self.patient.jedan.jedan_name.lower().startswith("seeg"):
            return date.strftime(self.patient.jedan_registered_date, "%m%d") + '-' + self.patient.jedan_registered_number
        else: 
            #print(self.patient.jedan_registered_date, "%y%m%d")
            return date.strftime(self.patient.jedan_registered_date, "%Y%m%d") + '-' + self.patient.jedan_registered_number
        
        
    
    def cnv_del_list(self):
        """
        Inside Pdf file we need to display cnv names 
        """
        # cnv_del_list = self.cnv_info.filter(ppv_npv__aneup_type = 'cnv_del')
        cnv_del_list = self.cnv_info.filter(ppv_npv__aneup_type = 'cnv_del').order_by('-id')

        
        
        cnv_del = ""
        for cnv_del_name in cnv_del_list :
            # cnv_del += f" {cnv_del_name.ppv_npv.name_long},"
            cnv_del += f" {cnv_del_name.cnv_name },"

        return cnv_del.strip(',') if cnv_del else "Low Risk" 


    def cnv_dup_list(self):
        cnv_dup_list = self.cnv_info.filter(ppv_npv__aneup_type = 'cnv_dup').order_by('-id')
        
        cnv_dup= ""
        for cnv_dup_name in cnv_dup_list:
            
            #cnv_dup += f" {cnv_dup_name.ppv_npv.name_long },"
            cnv_dup += f" {cnv_dup_name.cnv_name },"
            
        return cnv_dup.strip(',') if cnv_dup else "Low Risk" 
        
    def cnv_detail(self):
        cnv_all = self.cnv_info.all()
        cnv_detail = ""
        
        for cnv_name in cnv_all :
            cnv_detail += f" {cnv_name.cnv_detail} \n"

        return cnv_detail.strip(',')
        
        
        
        
    def cnv_final(self):
        """
        This will render filtered cnv if exist
        """
        try:
            cnv_final_files = glob.glob(os.path.join("media",self.sample_path(), '*final.cnv'))
            
            if cnv_final_files:
                final_file = cnv_final_files[0]
                df = pd.read_table(final_file)
                #! Ignore last column Reference Gene
                df = df.iloc[:, :-1]
                
                #! Filter on the basis of Size 
                
                
                #! Can Filter CNV that are shown in our List ONly 
                def apply_conditional_formatting(val):
                    if val in ['Dieorg', ]:
                        return 'background-color:red'
                
                # Apply the conditional formatting to the DataFrame
                styled_df = df.style.applymap(apply_conditional_formatting)

                # Render the styled DataFrame as an HTML table
                html_table = styled_df.to_html(index=False,
                    bold_headers=True).replace('nan', '')

                html_table = html_table.replace(
                    '<table', '<table class="table table-bordered table-hover"')

                html_table = html_table.replace(
                    '<th', '<th style="padding:5px;"')

                # Find and remove the index column from the table headers
                index_column_header = '<th class="blank"></th>'
                html_table = html_table.replace(index_column_header, '')

                return html_table
            else :
                return "<h1>Excel file with the CNV Finale (Filtered) doesnot exist </h1>"
            
        except FileNotFoundError:
            return "<h3>CNV Final(Filtered) Excel file not found</h3>"

        except pd.errors.EmptyDataError:
            return "<h3>CNV Final(Filtered) Excel file is empty</h3>"
        
        
    def cnv_filter(self):
        """
        This will render filtered cnv if exist
        """
        try:
            # f"media/{self.sample_path}/*final_filtered.cnv"
            cnv_final_path = os.path.join("media", self.sample_path() , "*_final_filtered.cnv" )
            
            # cnv_final_files = glob.glob( f"media/{self.sample_path}/*_final_filtered.cnv" )
            cnv_final_files = glob.glob( cnv_final_path )
            
            #print(cnv_final_files)
            
            if cnv_final_files:
                
                final_file = cnv_final_files[0]
                df = pd.read_csv(final_file)
                #! Ignore last column Reference Gene
                # df = df.iloc[:, :-1]
                #print(df)
                
                #! Can Filter CNV that are shown in our List ONly 
                def apply_conditional_formatting(val):
                    if val in ['Dieorg', ]:
                        return 'background-color:red'
                
                # Apply the conditional formatting to the DataFrame
                styled_df = df.style.applymap(apply_conditional_formatting)

                # Render the styled DataFrame as an HTML table
                html_table = styled_df.to_html(index=False,
                    bold_headers=True).replace('nan', '')

                html_table = html_table.replace(
                    '<table', '<table class="table table-bordered table-hover"')

                html_table = html_table.replace(
                    '<th', '<th style="padding:5px;"')

                # Find and remove the index column from the table headers
                index_column_header = '<th class="blank"></th>'
                html_table = html_table.replace(index_column_header, '')

                return html_table
            else :
                return "<h1>Excel file with the CNV Filtered doesnot exist </h1>"
            
        except FileNotFoundError:
            return "<h1>CNV Final Excel file not found</h1>"

        except pd.errors.EmptyDataError:
            return "<h1>CNV Final Excel file is empty</h1>"
        



    def cnv_ai_row_high_counts(self):
        "Counts how many High Predicts in Row File "

        try:
            # f"media/{self.sample_path}/*final_filtered.cnv"
            cnv_final_path = os.path.join(
                "media", self.sample_path(), "*cnv_dnn_raw.csv")

            # cnv_final_files = glob.glob( f"media/{self.sample_path}/*_final_filtered.cnv" )
            cnv_final_files = glob.glob(cnv_final_path)

            # print(cnv_final_files)

            if cnv_final_files:

                final_file = cnv_final_files[0]
                df = pd.read_csv(final_file)

                return df[df["ModelPredict"] == "High" ].shape[0]


            else:
                return "*"

        except FileNotFoundError:
            return " X "

        except pd.errors.EmptyDataError:
            return "<h1>CNV Final Excel file is empty</h1>"
        


    def cnv_ai_final_high_counts(self):
        "Counts how many High Predicts in Row File "

        try:
            # f"media/{self.sample_path}/*final_filtered.cnv"
            cnv_final_path = os.path.join(
                "media", self.sample_path(), "*cnv_dnn_filtered.csv")

            # cnv_final_files = glob.glob( f"media/{self.sample_path}/*_final_filtered.cnv" )
            cnv_final_files = glob.glob(cnv_final_path)

            # print(cnv_final_files)

            if cnv_final_files:

                final_file = cnv_final_files[0]
                df = pd.read_csv(final_file)

                return df[df["AdjustPredict"] == "High" ].shape[0]


            else:
                return "*"

        except FileNotFoundError:
            return " X "

        except pd.errors.EmptyDataError:
            return "<h1>CNV Final Excel file is empty</h1>"








    def cnv_ai_high_risk_list(self):
        "Counts how many High Predicts in Row File "

        try:
            # f"media/{self.sample_path}/*final_filtered.cnv"
            cnv_final_path = os.path.join(
                "media", self.sample_path(), "*cnv_dnn_filtered.csv")

            # cnv_final_files = glob.glob( f"media/{self.sample_path}/*_final_filtered.cnv" )
            cnv_final_files = glob.glob(cnv_final_path)

            # print(cnv_final_files)

            if cnv_final_files:

                final_file = cnv_final_files[0]
                df = pd.read_csv(final_file)

                df = df[df["AdjustPredict"] == "High" ].reset_index(drop= True) 
                if df.shape[0] >=1 :
                    

                    # list_to_render = df["AdjustPredict"].to_list()
                    list_to_render = df["Disease"].to_list()

                    return ", ".join(list_to_render)
                return "All Low"


            else:
                return "Error in loading cnv ai final file check model-> sample "

        except FileNotFoundError:
            return " File doesnot exist  "

        except pd.errors.EmptyDataError:
            return "Blank Data Frame "





    
    def cnv_ai_excel(self):
        """
        Display AI prediction High Risk Result 
        """
        try:
            # f"media/{self.sample_path}/*final_filtered.cnv"
            cnv_final_path = os.path.join(
                "media", self.sample_path(), "*ai_final_filtered.cnv")

            # cnv_final_files = glob.glob( f"media/{self.sample_path}/*_final_filtered.cnv" )
            cnv_final_files = glob.glob(cnv_final_path)

            # print(cnv_final_files)

            if cnv_final_files:

                final_file = cnv_final_files[0]
                df = pd.read_csv(final_file)
                #! Ignore last column Reference Gene
                # df = df.iloc[:, :-1]
                # print(df)

                #! Can Filter CNV that are shown in our List ONly
                def apply_conditional_formatting(val):
                    if val in ['Dieorg', ]:
                        return 'background-color:red'

                # Apply the conditional formatting to the DataFrame
                styled_df = df.style.applymap(apply_conditional_formatting)

                # Render the styled DataFrame as an HTML table
                html_table = styled_df.to_html(index=False,
                                               bold_headers=True).replace('nan', '')

                html_table = html_table.replace(
                    '<table', '<table class="table table-bordered table-hover"')

                html_table = html_table.replace(
                    '<th', '<th style="padding:5px;"')

                # Find and remove the index column from the table headers
                index_column_header = '<th class="blank"></th>'
                html_table = html_table.replace(index_column_header, '')

                return html_table
            else:
                return "<h1>Excel file with the CNV Filtered doesnot exist </h1>"

        except FileNotFoundError:
            return "<h1>CNV Final Excel file not found</h1>"

        except pd.errors.EmptyDataError:
            return "<h1>CNV Final Excel file is empty</h1>"
    
    

    def excel_aim(self):
        """
        load excel if exist and return to html div
        """
        try:
            excel_files = glob.glob(os.path.join(
                "media/", self.sample_path() , '*_AIM.xlsx'))
            if excel_files:
                file = excel_files[0]
                if file.endswith('_AIM.xlsx'):
                    
                    #! Columns to open 
                    models_ = ['RF',"VC", 'lgbm',   'xgb', 'mlp',  'DNN', "AK"] #! in scaled mlp is small 
        
                    cols_to_open =["Chr","Info", "Value"] + [model + "_RR" for model in models_] + [model + "_cnt" for model in models_]\
                        + ["1e-05_Z", "0.0005_Z", "0.001_Z", "0.05_Z"  ]
                    
                    df = pd.read_excel(file)
                    
                    # Filter cols_to_open list to include only columns that exist in the DataFrame
                    available_cols = [col for col in cols_to_open if col in df.columns]
                    
                    #! in whole ai model VC_Ratio (VC_RR) is not exist so 
                    df = df.loc[:, available_cols]
                    
                    
                    ordered_cols = [ "Chr","Info", "Value" ,
                                    "AK_RR","AK_cnt","DNN_RR","DNN_cnt","RF_RR","RF_cnt","VC_cnt" ,"VC_RR",
                                    "lgbm_RR","lgbm_cnt","mlp_RR", "mlp_cnt", "xgb_RR","xgb_cnt",
                                    "1e-05_Z", "0.0005_Z", "0.001_Z", "0.05_Z"     ]
                    
                    # Filter the ordered_cols list to include only columns that exist in the DataFrame
                    final_cols = [col for col in ordered_cols if col in df.columns]
        
                    try:
                        df= df.loc[:, final_cols ]
                    except Exception as err:
                        #print(err)
                        pass
                    
                    df.index += 1
                    
                    
                    def apply_conditional_formatting(val):
                        if val in  ['H','XXY','XYY',"XXX",'XO']:
                            return 'background-color:red'
                        

                    # Apply the conditional formatting to the DataFrame
                    styled_df = df.style.applymap(apply_conditional_formatting)
                                        
                    # Render the styled DataFrame as an HTML table
                    html_table = styled_df.to_html(bold_headers=True  ).replace('nan', '')
                    
                    html_table = html_table.replace(
                        '<table', '<table class="table table-bordered table-hover"')
                    
                    html_table = html_table.replace(
                        '<th', '<th style="padding:5px;"')
                    

                    
                    return html_table

            return "<h1>Excel file with the specified pattern not found</h1>"


        except FileNotFoundError:
            return "<h1>Excel file not found</h1>"

        except pd.errors.EmptyDataError:
            return "<h1>Excel file is empty</h1>"

    def excel_aim_rc(self):
        """
        load excel if exist and return to html div
        """
        try:
            excel_files = glob.glob(os.path.join(
                "media/", self.sample_path(), '*_AIM_rc.xlsx'))
            if excel_files:
                file = excel_files[0]
                if file.endswith('_AIM_rc.xlsx'):

                    #! Columns to open

                    df = pd.read_excel(file,  index_col=0)

                    # Rename the index column
                    df.index.name = 'Chromosome'

                    def apply_conditional_formatting(val):
                        if val in ['H', "T", 'T**', 'XXY', 'XYY', "XXX", 'XO']:
                            return 'background-color:red'

                    # Apply the conditional formatting to the DataFrame
                    styled_df = df.style.applymap(apply_conditional_formatting)

                    # Render the styled DataFrame as an HTML table
                    html_table = styled_df.to_html(
                        bold_headers=True).replace('nan', '')

                    html_table = html_table.replace(
                        '<table', '<table class="table table-bordered table-hover"')

                    html_table = html_table.replace(
                        '<th', '<th style="padding:5px;"')

                    return html_table

            return "<h1>Excel file with the specified pattern not found</h1>"

        except FileNotFoundError:
            return "<h1>Excel file not found</h1>"

        except pd.errors.EmptyDataError:
            return "<h1>Excel file is empty</h1>"



    
    
    # def jedan_pdf_show(self):
    #     """
    #     This Function will control weather to show or hide result pdf in Jedan Dashboard
    #     #! where to show delete after few days 20240523
    #     """
    #     return True
    
    def result_first_image(self):
        """
        to confirm weather sex information is properly printed or not 
        release management page 
        """
        result_image_root = os.path.join('media', self.sample_path(), "results")
        
        #! if folder doesnot exist then create which block error in page loading 
        os.makedirs(result_image_root, exist_ok=True)
        
        for file in os.listdir(result_image_root ):
            target_image_root = os.path.join(result_image_root, file )
            if os.path.isdir(target_image_root) :
                for result_image in os.listdir(target_image_root) :
                    first_png_image =os.path.join(target_image_root, result_image )
                    
                    if result_image.endswith("_1.jpg") or "_1_" in result_image :
                        #print(first_png_image)
                        return first_png_image
                    
                    
            
            
            
        return 'No image found '
        
    






    class Meta:
        verbose_name = "2.검체 정보 Sample"  # table header in admin sites
        verbose_name_plural = "2.검체  정보 Samples"
