<div class="card" style="width:100%;margin-bottom:3px;">
    <div class="table-responsive">


        <button type="button" class="row btn btn-primary btn-lg btn-block" style="margin-bottom: 10px;">
             Common fields for 판독  &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;   {{sample.tgc_s_id }} 
        </button> 



        <table class="table table-bordered table-striped" style="margin-bottom:0;">

            <thead class="thead-lite">

                                <tr>

                    <td td colspan="1" class="table-success" style="width:10%">
                        <h3> {{sample.service_type }} </h3>
                    </td>


                    <td colspan="6">
                        <div style="text-align: left;margin-left:20px">
                            <h3> {{sample.service_type.service_limit}}</h3>
                        </div>
                    </td>
                    
                    <td class="{% if sample.patient.patient_bmi > 30  %} table-danger {% endif %}">
                        BMI-{{ sample.patient.patient_bmi }}</td>




                    <td colspan="1">
                        <div style="text-align: left;margin-left:20px">
                            <h3> {{sample.samples_trisomy.not_low_aneps  }}  </h3>
                        </div>
                    </td>

                    <td>{{ sample.patient.get_fetus_number_display }} </td>




                    <td class="{% if sample.patient.ivf_treatment == '1'  %} table-danger {% endif %}"  > IVF-{{ sample.patient.get_ivf_treatment_display }} [ {{ sample.patient.ivf_number }} ]  </td>

                    <td>임신주수: {{ sample.patient_ga }} <br> 
                      <label class="{% if sample.ff_info.ffy_difference   > 1 or  sample.ff_info.ffy_risk_zone  %} bg-danger text-white {% else %}  bg-success text-white {% endif %} ">  
                                    성별: {{ sample.get_gender_display }} /   {{ sample.patient.doctor.sex_info }}    </ label > 
                    </td>

                    <td colspan="3" >
                        <div style="text-align: left;margin-left:10px" class='row'>
                            <h3> {{sample.tgc_s_id }}  </h3> &nbsp; &nbsp; &nbsp; <h4> {{sample.patient.jedan }}</h4>

                                <a role="button" target="_blank" class="btn  btn-info"
                    href="{% url 'hospital_report_detail' sample.patient.hospital.id %}">  {{sample.patient.hospital.hospital_name }}
                    
                    </a>
                                
                                
                                
                        </div>
                    </td>

                    

                </tr>
                



                <tr>
                    <th>
                        <h5> ID </h5>
                    </th>

                    <th>
                        <h5> UR </h5>
                    </th>
                    <th>
                        <h5> WGC </h5>
                    </th>

                    <th>
                        <h5> Seq1,2 </h5>
                    </th>

                     

                     <th>
                        <h5> GenoFF </h5>
                    </th>
                    

                    <th>
                        <h5> FFY </h5>
                    </th>
                    <th>
                        <h5> GenoAI </h5>
                    </th>

                    <th>
                        <h5> PCA AI </h5>
                    </th>

                    <th>
                        <h5> REF </h5>
                    </th>

                    <th>
                        <h5> Max-Z </h5>
                    </th>

                                        <th>
                        <h5> Min-Z </h5>
                    </th>


                    <th>
                        <h5> 1-차 판독</h5>
                    </th>                  
                    <th>

                        <h5> 2-차 판독</h5>
                    </th>

                                        <th>

                        <h5> 결과  </h5>
                    </th>

                    


                    <th>
                        <h5> 1-2 차 메모 </h5>
                    </th>

                    <th>
                        <h5> 1-2 차 판독자  </h5>
                    </th>

                </tr>
            </thead>

            <tbody>


                {% for sample_ in sample.patient.samples.all %}

                <tr {% if sample_.tgc_s_id == sample.tgc_s_id %} class="table-success" {% endif %}>
                    <td>
                        
                                                    <a target="_blank" class="btn btn-primary btn-sm btn-block"
                                href="/genomom/common/sample_detail/{{sample_.id}}">
                                 {{ sample_.tgc_s_id }}
                                </a>
                            
                            </td>

                    <td class="{% if sample_.ngs_info.unique_reads < 20_00_000 %} table-danger {% endif %} ">
                         {{sample_.ngs_info.unique_reads }} </td>

                    <td
                        class="{% if sample_.ngs_info.wgc_ratio > 0.43 or  sample_.ngs_info.wgc_ratio < 0.40 %} table-danger {% endif %}">
                        {{ sample_.ngs_info.wgc_ratio }} </td>

                    
                    <td
                        class="{% if sample_.ff_info.seq1 < 4  %} table-danger {% endif %}">
                        {{ sample_.ff_info.seq1|floatformat:2  }} , {{ sample_.ff_info.seq2|floatformat:2  }} </td>

                    
                    <td
                        class="{% if sample_.ff_info.genoff < 4  %} table-danger {% endif %}">
                        {{ sample_.ff_info.geno_ff|floatformat:2  }}</td>



                    <td
                        class="{% if sample_.ff_info.ff_y  > 1 and sample_.ff_info.ff_y < 4   %} table-danger {% endif %}">
                        {{ sample_.ff_info.ff_y|floatformat:2 }}</td>


                    <td class="{% if sample_.ff_info.geno_ai < 4  %} table-danger {% endif %}">
                        {{ sample_.ff_info.geno_ai|floatformat:2 }}</td>

                    <td class="{% if sample_.ff_info.geno_ai < 4  %} table-danger {% endif %}">
                        {{ sample_.ff_info.pca_ai|floatformat:2 }}</td>

                    <td class="{% if 'pippin' in sample_.sample_dna_qc.ref_type %} text-primary {% else %} text-danger {% endif %}">
                        {{ sample_.sample_dna_qc.get_ref_type_display }}
                    </td>

                    <td class="align-middle" >
                        <strong class="{% if sample_.zscore.max_zscore > 4.5 %}  bg-danger text-white {% endif %}" >  {{sample_.zscore.max_zscore }} </strong>
                    </td>


                    <td class="align-middle"  >    
                        <strong class="{% if sample_.zscore.min_zscore < -4 %} bg-danger text-white {% endif %}" > {{sample_.zscore.min_zscore }}  </strong>
                    </td> 



                    <td colspan="1">
                        <div >
                            <h5> {{sample_.samples_trisomy.get_final_result1_display }} </h5>
                        </div>
                    </td>


                    <td class="{% if sample_.samples_trisomy.final_result2 != '0'  %} table-danger {% endif %}">
                        {{sample_.samples_trisomy.get_final_result2_display }} </td>

                    <td >
                        <div >
                            <h5> {{sample_.samples_trisomy.final_result_short2|slice:6 }} </h5>
                        </div>
                    </td>


                    <td class="align-middle"  >
                        {{sample_.samples_trisomy.memo_1  }} <br>
                        {{sample_.samples_trisomy.memo_2  }}
                    </td>
                    

                    <td class="align-middle" >
                        {{sample_.samples_trisomy.persons_1  }} <br>
                        {{sample_.samples_trisomy.persons_2  }}
                    </td>





                </tr>



                {% endfor %}


      





            </tbody>
        </table>
        

    </div>
</div>




{% comment %} <div class="card" style="width:100%;">
    <div class="table-responsive">

               <table class="table table-bordered" >

            <thead class="thead-lite">
                <tr>
                    {% for i in range_24 %}
                        <th style="padding:0;">
                             <div style="padding: 10px;">
                                Chr{{ i }}
                            </div>
                        </th>
                    {% endfor %}
                </tr>
            </thead>

            <tbody>

                <tr>
                     {% for z in sample.zscore.all_z_score %}

                        <th style="padding:0;" class="{% if z > 4 or z < -5 %}table-danger{% endif %}">

                             <div style="padding: 10px;">
                                {{ z|floatformat:2 }}
                            </div>
                        </th>
                    {% endfor %}
                   

                </tr>



            </tbody>
        </table>


</div>
</div> {% endcomment %}