{% extends 'genomom/rookit_results/common_templates/base.html' %}
{% load static %}
{% block result_parts %}

{# ========================== Page 1 - 검사불능 결과 ========================== #}
{% include 'genomom/rookit_results/common_templates/nipt_header_basic.html' %}
<div class="page-container page-first">
  {% include 'genomom/rookit_results/common_templates/a_sample_patient_hospital_info.html' %}
  <div class="blank_height"></div>

  {% include 'genomom/rookit_results/common_templates/b_dna_qc_info.html' %}
  <div class="blank_height"></div>

  {% include 'genomom/rookit_results/common_templates/b_dna_qc_gender_info.html' %}
  <div class="blank_height"></div>

  {# 검사불능 결과 섹션 #}
  {% include 'genomom/rookit_results/common_templates/c_aneuploidy_info_result_fail.html' %}
  <div class="blank_height"></div>

  {# 검사불능 설명 섹션 #}
  {% include 'genomom/rookit_results/common_templates/c_aneuploidy_info_explanation_fail.html' %}

  {% include 'genomom/rookit_results/common_templates/footer.html' with page_number="1" only %}
</div>
<div class="page-break"></div>

{# ========================== Page 2 - 검사 방법 및 제한사항 ========================== #}
<div class="page-container">
  {% include 'genomom/rookit_results/common_templates/g_test_methods_limitation_reference.html' %}
  <div style="height: 8cm;"></div>

  {% include 'genomom/rookit_results/common_templates/j_sign_parts.html' %}
  {% include 'genomom/rookit_results/common_templates/k_supplier_sales_info.html' %}
  {% include 'genomom/rookit_results/common_templates/footer.html' with page_number="2" only %}
</div>

{% endblock result_parts %}
